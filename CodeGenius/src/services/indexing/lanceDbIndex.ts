import * as fs from 'fs';
import * as path from 'path';
import * as os from "os";

import { connect, Connection, Table, VectorIndexParams, WriteMode } from "vectordb";
import { codeChunker } from './chunk/code';
import { ChunkWithoutID, genToStrs } from './chunk/chunk';
import { ZeroAgentEmbedding } from './ZeroAgentEmbedding';
import { calculateFileHash, FileMetadata, getCodeFilesInDirectory, getNeedIndexingFiles, readFileByLine } from './util/paths';
import { ZeroAgentReranker } from './ZeroAgentReranker';
import { readJsonFile, writeJsonFile } from './evaluation';
import { EMBED_VECTOR_LEN, RETRIEVE_TOPN } from './constant';
import { IndexingProgressUpdate } from '@shared/ExtensionMessage';

export const MAX_CHUNK_LINES = 50;
export const MAX_CHUNK_BATCH = 50;
const MIN_ROWS_NEED_INDEX = 100000;
const ZERO_AGENT_GLOBAL_DIR = path.join(os.homedir(), ".zeroAgent");
const DEFAULT_DB_SAVE_DIR = path.join(ZERO_AGENT_GLOBAL_DIR, "index/lancedb");

export interface CodeChunkDict {
    filePath: string;
    startLine: number;
    endLine: number;
    vector: number[];
    [key: string]: any;
}

export interface indexResult {
    repoName: string;
    fileNum: number;
    chunkDuration: number;
    embeddingDuration: number;
    insertDataDuration: number;
    duration: number;
    dataRows: number;
}
const resultSaveDir = path.join(ZERO_AGENT_GLOBAL_DIR, "index/result");

export async function getCodeChunkDicts(
    chunks: ChunkWithoutID[],
    embeddings: number[][]) {
    const chunkDicts: CodeChunkDict[] = chunks.map((chunk, index) => ({
        filePath: chunk.filepath,
        startLine: chunk.startLine,
        endLine: chunk.endLine,
        vector: embeddings[index]
    }));
    return chunkDicts;
}

function pathToSafeFilename(pathStr: string): string {
    return pathStr
        .replace(/[^a-zA-Z0-9_\-.]/g, '_')
        .replace(/^[^a-zA-Z_]/, '_');
}

export class LanceDbIndex {
    private db!: Connection;
    private embedModel: ZeroAgentEmbedding;
    private rerankModel: ZeroAgentReranker;
    private repoBaseDir!: string;
    private dbSaveDir: string;
    private tableName!: string;
    private table!: Table;
    private fileIndexingInfo: Record<string, FileMetadata>;
    private result: indexResult;

    constructor() {
        this.dbSaveDir = DEFAULT_DB_SAVE_DIR;
        this.embedModel = new ZeroAgentEmbedding();
        this.rerankModel = new ZeroAgentReranker();
        this.fileIndexingInfo = {};
        this.result = {
            repoName: '',
            fileNum: 0,
            chunkDuration: 0,
            embeddingDuration: 0,
            insertDataDuration: 0,
            duration: 0,
            dataRows: 0
        };
    }

    async indexing(repoBaseDir: string, isRetrieve: boolean = false, onProgress?: (update: IndexingProgressUpdate) => void) {
        this.repoBaseDir = repoBaseDir;
        this.tableName = pathToSafeFilename(repoBaseDir);
        this.result.repoName = this.tableName;
        console.log("tableName:", this.tableName);
        if (!this.db) {
            this.db = await connect(this.dbSaveDir);
            if (!this.db) console.error("failed to connect to database!");
            if (!(await this.db.tableNames()).includes(this.tableName)) {
                if (isRetrieve) {
                    console.log("index not create!");
                    return;
                }
                console.log(`table ${this.tableName} not exist, start full indexing...`);
                await this.createTable();
                await this.fullIndexing(onProgress);
                await this.createIndex();
                console.log("full index infos:", this.result);
            }
            this.table = await this.db.openTable(this.tableName);
            if (!this.table) console.error("failed to create table!");
        }
        if (!this.table) {
            this.table = await this.db.openTable(this.tableName);
            if (!this.table) console.error("failed to open table!");
        }
        if (!isRetrieve) {
            this.update(onProgress);
        }
    }

    async createTable() {
        console.log("start create table...");
        if (!this.db) {
            this.db = await connect(this.dbSaveDir);
        }
        if (!this.db) {
            console.error("failed to connect to database!");
        }
        const array: number[] = new Array(EMBED_VECTOR_LEN).fill(0);
        const exampleData: CodeChunkDict = {
            filePath: "example/path",
            startLine: 1,
            endLine: 10,
            vector: array
        };
        this.table = await this.db.createTable(this.tableName, [exampleData], { writeMode: WriteMode.Overwrite });
        if (!this.table) {
            console.error("failed create table");
        }
        await this.table.delete(`'filePath' = 'example/path'`);
        console.log("create table success：", this.table.name);
    }

    async insertData(data: CodeChunkDict[]) {
        const startTime = performance.now();
        if (!this.db || !this.table) {
            console.error("database or table not exist!");
        }
        if (data.length === 0) {
            return;
        }
        await this.table.add(data);
        const endTime = performance.now();
        const duration = endTime - startTime;
        this.result.insertDataDuration += duration;
    }

    async fullIndexing(onProgress?: (update: IndexingProgressUpdate) => void) {
        const codeFiles = await getCodeFilesInDirectory(this.repoBaseDir);
        console.log("file nums:", codeFiles.length);
        this.result.fileNum = codeFiles.length;

        await this.embedding(
            codeFiles,
            onProgress
        );
    }

    async embedding(codeFiles: string[],
        onProgress?: (update: IndexingProgressUpdate) => void
    ) {
        const startTime = performance.now();
        let chunkBatch: ChunkWithoutID[] = [];
        const chunksConcurrency: ChunkWithoutID[][] = [];

        for (let i = 0; i < codeFiles.length; i++) {
            const current_file = codeFiles[i];
            const progress = i / codeFiles.length;

            if (onProgress) {
                onProgress({
                    progress: progress,
                    status: "indexing"
                });
            }

            const chunks = await this.getChunks(current_file);
            if (chunks.length === 0) {
                await this.getFileMetadata(current_file);
                continue;
            }
            chunkBatch.push(...chunks);
            while (chunkBatch.length >= MAX_CHUNK_BATCH) {
                chunksConcurrency.push(chunkBatch.slice(0, MAX_CHUNK_BATCH));
                const contentsMap = chunksConcurrency.map(chunks => chunks.map(chunk => chunk.content))
                if (contentsMap.length >= this.embedModel.getEmbeddingConcurrency()) {
                    const embeddingsMap = await this.getBatchEmbeddings(contentsMap);
                    while (embeddingsMap.length > 0) {
                        const chunks = chunksConcurrency.pop();
                        const embeddings = embeddingsMap.pop();
                        if (chunks && embeddings && embeddings.length > 0) {
                            const chunkDicts = await getCodeChunkDicts(chunks, embeddings);
                            await this.insertData(chunkDicts);
                        }
                    }
                }
                chunkBatch = chunkBatch.slice(MAX_CHUNK_BATCH);
            }
            await this.getFileMetadata(current_file);
        }

        // 处理剩余的代码块
        if (chunkBatch.length > 0) {
            chunksConcurrency.push(chunkBatch);
        }

        const contentsMap = chunksConcurrency.map(chunks => chunks.map(chunk => chunk.content))
        const embeddingsMap = await this.getBatchEmbeddings(contentsMap);
        while (embeddingsMap.length > 0) {
            const chunks = chunksConcurrency.pop();
            const embeddings = embeddingsMap.pop();
            if (chunks && embeddings && embeddings.length > 0) {
                const chunkDicts = await getCodeChunkDicts(chunks, embeddings);
                await this.insertData(chunkDicts);
            }
        }

        const endTime = performance.now();
        const duration = endTime - startTime;
        const rows = await this.table.countRows();
        this.result.dataRows = rows;
        this.result.duration = duration;
        this.saveFileIndexingInfo();

        if (onProgress) {
            onProgress({
                progress: 1.0,
                status: "done"
            });
        }
    }

    async createIndex() {
        console.log("start indexing...");
        if (!this.db || !this.table) {
            console.error("database or table not exist!");
            return;
        }
        const dataRows: number = await this.table.countRows();
        if (dataRows < MIN_ROWS_NEED_INDEX) {
            console.log("the number of data rows is too small, no need to create index!");
            return;
        }
        const indexParams: VectorIndexParams = {
            type: "ivf_pq",
            num_partitions: 4,
            num_sub_vectors: 8,
            num_bits: 4,
            use_opq: false,
            index_cache_size: 128
        }
        await this.table.createIndex(indexParams);
        console.log("create index success!");
    }

    async retrieve(repoBaseDir: string, task: string) {
        console.log("workspace:", repoBaseDir);
        await this.indexing(repoBaseDir, true);
        if (!this.db || !this.table) {
            console.log("database or table not exist!");
            return [];
        }
        console.log("start retrieve:", task);
        const embedding = await this.getEmbeddings([task]);
        const results: CodeChunkDict[] = await this.table.search(embedding[0]).limit(RETRIEVE_TOPN).execute();
        return await this.processRetrieveResults(results);
    }

    async getRerankScore(task: string, retrieveResults: CodeChunkDict[]) {
        const queryNodes: string[][] = [];
        for (let i = 0; i < retrieveResults.length; i++) {
            const result: CodeChunkDict = retrieveResults[i];
            const node: string[] = [];
            const content = await readFileByLine(
                result["filePath"],
                result["startLine"],
                result["endLine"] + 1
            );
            node.push(task);
            node.push(content);
            queryNodes.push(node);
        }
        const score: number[] = await this.rerankModel.rerank(queryNodes);
        return score;
    }

    async sortAndRerank(numbers: number[], chunks: CodeChunkDict[]): Promise<CodeChunkDict[]> {
        if (numbers.length !== chunks.length) {
            return chunks;
        }
        const pairedArray: Array<{ num: number; chunk: CodeChunkDict }> = [];
        for (let i = 0; i < numbers.length; i++) {
            pairedArray.push({
                num: numbers[i],
                chunk: chunks[i]
            });
        }
        pairedArray.sort((a, b) => b.num - a.num);
        const result: CodeChunkDict[] = pairedArray.map(item => item.chunk);
        return result;
    }

    async retrieveByFilepath(filePath: string) {
        if (!this.db || !this.table) {
            console.error("database or table not exist!");
            return;
        }
        const results = await this.table.filter(`filePath = '${filePath}'`).execute();
    }

    async processRetrieveResults(results: CodeChunkDict[]): Promise<string[]> {
        let filePaths: string[] = [];
        for (let i = 0; i < results.length; i++) {
            if (filePaths.includes(results[i]["filePath"])) {
                continue;
            }
            filePaths.push(results[i]["filePath"]);
        }
        return filePaths;
    }

    async update(onProgress?: (update: IndexingProgressUpdate) => void) {
        await this.readFileIndexingInfo();
        const codeFiles = await getCodeFilesInDirectory(this.repoBaseDir);
        const needIndexingFiles: string[] = await getNeedIndexingFiles(codeFiles, this.fileIndexingInfo);
        await this.deleteByFilepath(needIndexingFiles);
        await this.embedding(needIndexingFiles, onProgress);
    }

    async getFileMetadata(filePath: string) {
        const hash: string = await calculateFileHash(filePath);
        const indexTime = Date.now();
        const metadata: FileMetadata = {
            "indexTime": indexTime,
            "hash": hash
        }
        this.fileIndexingInfo[filePath] = metadata;
    }

    async saveFileIndexingInfo() {
        const savePath = path.join(ZERO_AGENT_GLOBAL_DIR, `index/${this.tableName}.json`);
        await writeJsonFile(savePath, this.fileIndexingInfo);
    }

    async readFileIndexingInfo() {
        const savePath = path.join(ZERO_AGENT_GLOBAL_DIR, `index/${this.tableName}.json`);
        if (!fs.existsSync(savePath)) {
            return;
        }
        this.fileIndexingInfo = await readJsonFile(savePath);
    }

    async deleteByFilepath(filePaths: string[]) {
        if (!this.db || !this.table) {
            console.error("database or table not exist!");
            return;
        }
        for (let i = 0; i < filePaths.length; i++) {
            const filePath = filePaths[i];
            const condition = "`filePath` = " + `'${filePath}'`;
            await this.table.delete(condition);
        }
    }

    async getChunks(filePath: string): Promise<ChunkWithoutID[]> {
        const startTime = performance.now();
        const content = await fs.promises.readFile(filePath, 'utf-8');
        const generator = await codeChunker(filePath, content, MAX_CHUNK_LINES);
        const chunks = await genToStrs(generator);
        const endTime = performance.now();
        const duration = endTime - startTime;
        this.result.chunkDuration += duration;
        return chunks;
    }

    async getBatchEmbeddings(contents: string[][]): Promise<number[][][]> {
        const startTime = performance.now();
        const embeddings = await this.embedModel.getBatchEmbeddings(contents);
        this.result.embeddingDuration += (performance.now() - startTime);
        return embeddings;
    }

    async getEmbeddings(contents: string[]): Promise<number[][]> {
        const startTime = performance.now();
        const embeddings = await this.embedModel.getEmbeddings(contents);
        this.result.embeddingDuration += (performance.now() - startTime);
        return embeddings;
    }
}

export async function createIndex(repoBaseDir: string) {
    const lanceDb = new LanceDbIndex();
    await lanceDb.indexing(repoBaseDir, false);
}