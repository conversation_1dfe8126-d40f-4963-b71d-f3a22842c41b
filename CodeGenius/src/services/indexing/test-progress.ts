/**
 * 测试索引进度功能的示例代码
 * 这个文件展示了如何使用新的详细进度回调功能
 */

import { LanceDbIndex } from './lanceDbIndex';
import { IndexingProgressUpdate } from '@shared/ExtensionMessage';

// 模拟进度回调函数
function mockProgressCallback(update: IndexingProgressUpdate) {
    console.log('=== 索引进度更新 ===');
    console.log(`状态: ${update.status}`);
    console.log(`进度: ${(update.progress * 100).toFixed(1)}%`);
    console.log(`描述: ${update.desc || '无'}`);
    
    if (update.currentFile) {
        console.log(`当前文件: ${update.currentFile}`);
    }
    
    if (update.currentPhase) {
        console.log(`当前阶段: ${update.currentPhase}`);
    }
    
    if (update.processedFiles !== undefined && update.totalFiles !== undefined) {
        console.log(`文件进度: ${update.processedFiles}/${update.totalFiles}`);
    }
    
    if (update.chunksProcessed !== undefined && update.totalChunks !== undefined) {
        console.log(`代码块进度: ${update.chunksProcessed}/${update.totalChunks}`);
    }
    
    if (update.processingSpeed !== undefined && update.processingSpeed > 0) {
        console.log(`处理速度: ${update.processingSpeed} 文件/秒`);
    }
    
    if (update.estimatedTimeRemaining !== undefined && update.estimatedTimeRemaining > 0) {
        const minutes = Math.floor(update.estimatedTimeRemaining / 60);
        const seconds = update.estimatedTimeRemaining % 60;
        console.log(`预估剩余时间: ${minutes}分${seconds}秒`);
    }
    
    console.log('==================');
}

// 测试函数
export async function testIndexingProgress(repoPath: string) {
    console.log('开始测试索引进度功能...');
    
    const lanceDb = new LanceDbIndex();
    
    try {
        await lanceDb.indexing(repoPath, false, mockProgressCallback);
        console.log('索引完成！');
    } catch (error) {
        console.error('索引失败:', error);
    }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    const testPath = process.argv[2] || './';
    testIndexingProgress(testPath);
}
