import { useEffect, useState } from "react";
import IndexingProgressBar from "./IndexingProgressBar";
import IndexingProgressIndicator from "./IndexingProgressIndicator";
import IndexingProgressTitleText from "./IndexingProgressTitleText";
import IndexingProgressErrorText from "./IndexingProgressErrorText";
import IndexingProgressDetails from "./IndexingProgressDetails";
import { IndexingProgressUpdate } from "@shared/ExtensionMessage";
import IndexingProgressSubtext from "./IndexingProgressSubtext";
import { useExtensionState } from "@/context/ExtensionStateContext";

export function getProgressPercentage(
    progress: IndexingProgressUpdate["progress"],
) {
    return Math.min(100, Math.max(0, progress * 100));
}

function IndexingProgress() {
    const { indexProgress } = useExtensionState()
    const [update, setUpdate] = useState<IndexingProgressUpdate>({
        progress: 0.0,
        status: "loading",
    })

    // 不显示进度条下方文字链接
    let showSubText = false;
    // If sidebar is opened after extension initializes, retrieve saved states.
    let initialized = false;

    useEffect(() => {
        if (!initialized) {
            // Triggers retrieval for possible non-default states set prior to IndexingProgressBar initialization
            initialized = true;
        }
    }, []);

    useEffect(() => {
        // Use indexProgress if available, otherwise fall back to simple progress
        if (indexProgress) {
            setUpdate({
                progress: indexProgress.progress,
                desc: indexProgress.desc,
                status: indexProgress.status as IndexingProgressUpdate["status"]
            })
        }
    }, [indexProgress])

    function onClick() {
        switch (update.status) {
            case "failed":
                break
            case "indexing":
            case "loading":
            case "paused":
                // TODO: Implement pause/resume functionality
                break
            case "disabled":
                break
            case "done":
            default:
                break
        }
    }

    return (
        <div className="mt-4 flex flex-col">
            <div className="mb-0 flex justify-between text-sm">
                <IndexingProgressTitleText update={update} />
                {update.status !== "loading" && (
                    <IndexingProgressIndicator update={update} />
                )}
            </div>

            <IndexingProgressBar update={update} />

            {/* 显示详细的索引进度信息 */}
            <IndexingProgressDetails update={update} />

            {showSubText && (<IndexingProgressSubtext update={update} onClick={onClick} />)}

            {update.status === "failed" && (
                <div className="mt-4">
                    <IndexingProgressErrorText update={update} />
                </div>
            )}
        </div>
    );
}

export default IndexingProgress
