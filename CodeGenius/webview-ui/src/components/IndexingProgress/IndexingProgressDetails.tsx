import { IndexingProgressUpdate } from "@shared/ExtensionMessage"

export interface IndexingProgressDetailsProps {
  update: IndexingProgressUpdate;
}

const PHASE_TO_TEXT: Record<NonNullable<IndexingProgressUpdate["currentPhase"]>, string> = {
  scanning: "扫描文件",
  chunking: "分析代码",
  embedding: "生成向量",
  inserting: "插入数据",
  indexing: "创建索引",
}

const PHASE_TO_ICON: Record<NonNullable<IndexingProgressUpdate["currentPhase"]>, string> = {
  scanning: "🔍",
  chunking: "📝",
  embedding: "🧠",
  inserting: "💾",
  indexing: "⚡",
}

function IndexingProgressDetails({ update }: IndexingProgressDetailsProps) {
  if (update.status !== "indexing" || !update.currentPhase) {
    return null;
  }

  return (
    <div className="mt-2 p-3 bg-gray-50 rounded-md border">
      <div className="flex items-center gap-2 mb-2">
        <span className="text-lg">{PHASE_TO_ICON[update.currentPhase]}</span>
        <span className="font-medium text-sm">
          {PHASE_TO_TEXT[update.currentPhase]}
        </span>
      </div>
      
      {/* 当前文件信息 */}
      {update.currentFile && (
        <div className="mb-2">
          <div className="text-xs text-gray-600 mb-1">当前文件:</div>
          <div className="text-xs font-mono bg-white p-2 rounded border truncate" title={update.currentFile}>
            {update.currentFile}
          </div>
        </div>
      )}
      
      {/* 进度统计 */}
      <div className="grid grid-cols-2 gap-4 text-xs">
        {/* 文件进度 */}
        {(update.processedFiles !== undefined && update.totalFiles !== undefined) && (
          <div>
            <div className="text-gray-600 mb-1">文件进度:</div>
            <div className="font-medium">
              {update.processedFiles} / {update.totalFiles}
              <span className="text-gray-500 ml-1">
                ({Math.round((update.processedFiles / update.totalFiles) * 100)}%)
              </span>
            </div>
          </div>
        )}
        
        {/* 代码块进度 */}
        {(update.chunksProcessed !== undefined && update.totalChunks !== undefined && update.totalChunks > 0) && (
          <div>
            <div className="text-gray-600 mb-1">代码块:</div>
            <div className="font-medium">
              {update.chunksProcessed} / {update.totalChunks}
              <span className="text-gray-500 ml-1">
                ({Math.round((update.chunksProcessed / update.totalChunks) * 100)}%)
              </span>
            </div>
          </div>
        )}
        
        {/* 处理速度 */}
        {(update.processingSpeed !== undefined && update.processingSpeed > 0) && (
          <div>
            <div className="text-gray-600 mb-1">处理速度:</div>
            <div className="font-medium">
              {update.processingSpeed} 文件/秒
            </div>
          </div>
        )}
        
        {/* 预估剩余时间 */}
        {(update.estimatedTimeRemaining !== undefined && update.estimatedTimeRemaining > 0) && (
          <div>
            <div className="text-gray-600 mb-1">预估剩余:</div>
            <div className="font-medium">
              {Math.floor(update.estimatedTimeRemaining / 60)}分{update.estimatedTimeRemaining % 60}秒
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default IndexingProgressDetails
