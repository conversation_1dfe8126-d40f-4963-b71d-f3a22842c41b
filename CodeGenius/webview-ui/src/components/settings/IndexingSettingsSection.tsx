import i18n from "@/i18n"
import IndexingProgress from "../IndexingProgress"

function IndexingSettingsSection() {
  const config = {
    disableIndexing: false
  }

  return (
    <div className="py-5">
      <div>
        <h3 className="mx-auto mb-1 mt-0 text-xl">{i18n.get('setting.indexingSettingsSection.codebaseIndex')}</h3>
        <span className="text-lightgray w-3/4 text-xs">
          {i18n.get('setting.indexingSettingsSection.codebaseIndexDesc')}
        </span>
      </div>
      {config.disableIndexing ? (
        <div className="pb-2 pt-5">
          <p className="py-1 text-center font-semibold">Indexing is disabled</p>
          <p className="text-lightgray cursor-pointer text-center text-xs">
            Open settings and toggle <code>Disable Indexing</code> to re-enable
          </p>
        </div>
      ) : (
        <IndexingProgress />
      )}
    </div>
  )
}

export default IndexingSettingsSection
